package io.livekit.android.example.voiceassistant.ui

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import io.livekit.android.example.voiceassistant.datastreams.ChatMessage
import io.livekit.android.room.participant.Participant
import io.livekit.android.room.types.TranscriptionSegment
import androidx.compose.material3.MaterialTheme

/**
 * Displays a chat message item, handling both user and agent messages
 */
@Composable
fun ChatMessageItem(
    message: ChatMessage,
    isFromUser: Boolean,
    modifier: Modifier = Modifier
) {
    Box(
        modifier = modifier
            .fillMaxWidth()
            .padding(horizontal = 16.dp, vertical = 8.dp)
    ) {
        when {
            isFromUser -> {
                when (message) {
                    is ChatMessage.TranscriptionMessage -> {
                        UserTranscription(
                            transcription = message.transcriptionSegment,
                            modifier = Modifier.align(Alignment.CenterEnd)
                        )
                    }
                    is ChatMessage.TextMessage -> {
                        UserTextMessage(
                            text = message.text,
                            modifier = Modifier.align(Alignment.CenterEnd)
                        )
                    }
                }
            }
            else -> {
                AgentMessage(
                    text = message.text,
                    modifier = Modifier.align(Alignment.CenterStart)
                )
            }
        }
    }
}

@Preview(showBackground = true)
@Composable
fun ChatMessageItemPreview() {
    MaterialTheme {
        ChatMessageItem(
            message = ChatMessage.TextMessage(
                identity = Participant.Identity("user123"),
                text = "Hello, this is a sample message!",
                isFromUser = true
            ),
            isFromUser = true
        )
    }
}

@Preview(showBackground = true)
@Composable
fun ChatMessageItemAgentPreview() {
    MaterialTheme {
        ChatMessageItem(
            message = ChatMessage.TranscriptionMessage(
                identity = Participant.Identity("agent"),
                transcriptionSegment = TranscriptionSegment(
                    id = "agent-msg",
                    text = "Hello! I'm your AI assistant. How can I help you today?",
                    language = "en",
                    final = true,
                    firstReceivedTime = System.currentTimeMillis(),
                    lastReceivedTime = System.currentTimeMillis()
                )
            ),
            isFromUser = false
        )
    }
}
