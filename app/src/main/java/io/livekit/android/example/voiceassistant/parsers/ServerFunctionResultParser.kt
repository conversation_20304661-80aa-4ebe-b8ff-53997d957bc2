package io.livekit.android.example.voiceassistant.parsers

import io.livekit.android.example.voiceassistant.data.*
import io.livekit.android.example.voiceassistant.utils.BankAssistLogger
import kotlinx.serialization.json.*

/**
 * Parser for server function results
 */
class ServerFunctionResultParser {
    
    private val json = Json {
        ignoreUnknownKeys = true
        isLenient = true
    }
    
    /**
     * Parse JSON string into ServerFunctionResult
     */
    fun parseServerFunctionResult(jsonString: String): ServerFunctionResult? {
        return try {
            json.decodeFromString<ServerFunctionResult>(jsonString)
        } catch (e: Exception) {
            BankAssistLogger.e("Failed to parse server function result", e)
            null
        }
    }
    
    /**
     * Parse function result into specific banking operation result
     */
    fun parseFunctionResult(functionResult: ServerFunctionResult): ParsedFunctionResult {
        return try {
            when (functionResult.function.name) {
                BankingFunctions.GET_TRANSACTION_HISTORY -> parseTransactionHistory(functionResult)
                BankingFunctions.CHECK_BALANCE,
                BankingFunctions.GET_ACCOUNT_BALANCE -> parseAccountBalance(functionResult)
                BankingFunctions.TRANSFER_FUNDS,
                BankingFunctions.TRANSFER_FUNDS_ALT -> parseFundTransfer(functionResult)
                BankingFunctions.APPLY_FOR_LOAN,
                BankingFunctions.REQUEST_LOAN_INFO -> parseLoanApplication(functionResult)
                BankingFunctions.AUTHENTICATE_USER,
                BankingFunctions.VERIFY_IDENTITY,
                BankingFunctions.VERIFY_IDENTITY_ALT -> parseAuthentication(functionResult)
                else -> ParsedFunctionResult.Unknown(
                    functionName = functionResult.function.name,
                    rawOutput = functionResult.function.output
                )
            }
        } catch (e: Exception) {
            BankAssistLogger.e("Failed to parse function result for ${functionResult.function.name}", e)
            ParsedFunctionResult.Error(
                message = "Failed to parse function result: ${e.message}",
                code = "PARSE_ERROR"
            )
        }
    }
    
    private fun parseTransactionHistory(result: ServerFunctionResult): ParsedFunctionResult {
        val output = result.function.output
        val limit = result.function.arguments["limit"]?.jsonPrimitive?.intOrNull
        
        // Try to extract transaction data from the output text
        val transactions = extractTransactionsFromText(output)
        
        return ParsedFunctionResult.TransactionHistory(
            transactions = transactions,
            limit = limit
        )
    }
    
    private fun parseAccountBalance(result: ServerFunctionResult): ParsedFunctionResult {
        val output = result.function.output
        
        // Extract balance information from text
        val balances = extractBalancesFromText(output)
        
        return ParsedFunctionResult.AccountBalance(balances = balances)
    }
    
    private fun parseFundTransfer(result: ServerFunctionResult): ParsedFunctionResult {
        val output = result.function.output
        val args = result.function.arguments
        
        return ParsedFunctionResult.FundTransfer(
            transferId = generateTransferId(),
            status = if (output.contains("success", ignoreCase = true)) "SUCCESS" else "PENDING",
            amount = args["amount"]?.jsonPrimitive?.doubleOrNull ?: 0.0,
            fromAccount = args["from_account"]?.jsonPrimitive?.contentOrNull ?: "Unknown",
            toAccount = args["to_account"]?.jsonPrimitive?.contentOrNull ?: "Unknown",
            fee = args["fee"]?.jsonPrimitive?.doubleOrNull
        )
    }
    
    private fun parseLoanApplication(result: ServerFunctionResult): ParsedFunctionResult {
        val output = result.function.output
        val args = result.function.arguments
        
        return ParsedFunctionResult.LoanApplication(
            applicationId = generateApplicationId(),
            status = "SUBMITTED",
            amount = args["amount"]?.jsonPrimitive?.doubleOrNull ?: 0.0,
            interestRate = args["interest_rate"]?.jsonPrimitive?.doubleOrNull,
            term = args["term"]?.jsonPrimitive?.intOrNull
        )
    }
    
    private fun parseAuthentication(result: ServerFunctionResult): ParsedFunctionResult {
        val output = result.function.output
        val success = output.contains("success", ignoreCase = true) || 
                     output.contains("authenticated", ignoreCase = true)
        
        return ParsedFunctionResult.Authentication(
            success = success,
            method = "voice_verification",
            sessionToken = if (success) generateSessionToken() else null,
            expiresAt = if (success) System.currentTimeMillis() + (30 * 60 * 1000) else null // 30 minutes
        )
    }
    
    private fun extractTransactionsFromText(text: String): List<TransactionData> {
        // This is a simplified parser - in a real app, you'd have more sophisticated parsing
        val transactions = mutableListOf<TransactionData>()
        
        // Look for transaction patterns in the text
        val lines = text.split("\n")
        var transactionId = 1
        
        for (line in lines) {
            if (line.contains("$") && (line.contains("deposit") || line.contains("withdrawal") || 
                line.contains("transfer") || line.contains("payment"))) {
                
                val amount = extractAmountFromLine(line)
                val type = extractTypeFromLine(line)
                val description = line.trim()
                
                if (amount != null && type != null) {
                    transactions.add(
                        TransactionData(
                            id = "txn_$transactionId",
                            type = type,
                            amount = amount,
                            description = description,
                            timestamp = System.currentTimeMillis() - (transactionId * ********L), // Spread over days
                            balance = 15000.0 + (transactionId * 100) // Mock balance
                        )
                    )
                    transactionId++
                }
            }
        }
        
        // If no transactions found in text, return mock data
        if (transactions.isEmpty()) {
            return listOf(
                TransactionData("1", "deposit", 2500.0, "USD", "Recent salary deposit", System.currentTimeMillis() - ********, 15420.75),
                TransactionData("2", "withdrawal", -45.50, "USD", "Coffee shop purchase", System.currentTimeMillis() - ********, 12920.75),
                TransactionData("3", "transfer", -500.0, "USD", "Transfer to savings account", System.currentTimeMillis() - ********, 12420.75)
            )
        }
        
        return transactions
    }
    
    private fun extractBalancesFromText(text: String): List<AccountBalanceData> {
        val balances = mutableListOf<AccountBalanceData>()
        
        // Look for balance patterns
        val balanceRegex = Regex("""\$?([\d,]+\.?\d*)""")
        val matches = balanceRegex.findAll(text)
        
        if (matches.any()) {
            val amount = matches.first().groupValues[1].replace(",", "").toDoubleOrNull() ?: 15420.75
            balances.add(
                AccountBalanceData(
                    accountId = "checking-1234",
                    balance = amount,
                    accountType = "Checking"
                )
            )
        } else {
            // Default mock data
            balances.addAll(listOf(
                AccountBalanceData("checking-1234", 15420.75, "USD", "Checking"),
                AccountBalanceData("savings-5678", 8750.50, "USD", "Savings")
            ))
        }
        
        return balances
    }
    
    private fun extractAmountFromLine(line: String): Double? {
        val amountRegex = Regex("""\$?([\d,]+\.?\d*)""")
        val match = amountRegex.find(line)
        return match?.groupValues?.get(1)?.replace(",", "")?.toDoubleOrNull()
    }
    
    private fun extractTypeFromLine(line: String): String? {
        return when {
            line.contains("deposit", ignoreCase = true) -> "deposit"
            line.contains("withdrawal", ignoreCase = true) -> "withdrawal"
            line.contains("transfer", ignoreCase = true) -> "transfer"
            line.contains("payment", ignoreCase = true) -> "payment"
            else -> null
        }
    }
    
    private fun generateTransferId(): String = "TXF_${System.currentTimeMillis()}"
    private fun generateApplicationId(): String = "LOAN_${System.currentTimeMillis()}"
    private fun generateSessionToken(): String = "SESSION_${System.currentTimeMillis()}"
}
