package io.livekit.android.example.voiceassistant.data

import kotlinx.serialization.Serializable
import kotlinx.serialization.json.JsonElement

/**
 * Represents a function result received from the server
 */
@Serializable
data class ServerFunctionResult(
    val type: String,
    val timestamp: String,
    val function: FunctionInfo
)

@Serializable
data class FunctionInfo(
    val name: String,
    val arguments: Map<String, JsonElement>,
    val output: String
)

/**
 * Parsed function results for specific banking operations
 */
sealed class ParsedFunctionResult {
    data class TransactionHistory(
        val transactions: List<TransactionData>,
        val limit: Int? = null
    ) : ParsedFunctionResult()
    
    data class AccountBalance(
        val balances: List<AccountBalanceData>
    ) : ParsedFunctionResult()
    
    data class FundTransfer(
        val transferId: String,
        val status: String,
        val amount: Double,
        val fromAccount: String,
        val toAccount: String,
        val fee: Double? = null
    ) : ParsedFunctionResult()
    
    data class LoanApplication(
        val applicationId: String,
        val status: String,
        val amount: Double,
        val interestRate: Double? = null,
        val term: Int? = null
    ) : ParsedFunctionResult()
    
    data class Authentication(
        val success: Boolean,
        val method: String,
        val sessionToken: String? = null,
        val expiresAt: Long? = null
    ) : ParsedFunctionResult()
    
    data class Error(
        val message: String,
        val code: String? = null
    ) : ParsedFunctionResult()
    
    data class Unknown(
        val functionName: String,
        val rawOutput: String
    ) : ParsedFunctionResult()
}

@Serializable
data class TransactionData(
    val id: String,
    val type: String,
    val amount: Double,
    val currency: String = "USD",
    val description: String,
    val timestamp: Long,
    val balance: Double? = null,
    val category: String? = null
)

@Serializable
data class AccountBalanceData(
    val accountId: String,
    val balance: Double,
    val currency: String = "USD",
    val accountType: String,
    val lastUpdated: Long = System.currentTimeMillis()
)

/**
 * Function name mappings for banking operations
 */
object BankingFunctions {
    const val GET_TRANSACTION_HISTORY = "get_transaction_history"
    const val CHECK_BALANCE = "check_balance"
    const val GET_ACCOUNT_BALANCE = "getAccountBalance"
    const val TRANSFER_FUNDS = "transfer_money"
    const val TRANSFER_FUNDS_ALT = "transferFunds"
    const val APPLY_FOR_LOAN = "apply_for_loan"
    const val REQUEST_LOAN_INFO = "requestLoanInfo"
    const val AUTHENTICATE_USER = "authenticate_user"
    const val VERIFY_IDENTITY = "verify_identity"
    const val VERIFY_IDENTITY_ALT = "verifyIdentity"
    
    /**
     * Get all known banking function names
     */
    fun getAllFunctionNames(): Set<String> = setOf(
        GET_TRANSACTION_HISTORY,
        CHECK_BALANCE,
        GET_ACCOUNT_BALANCE,
        TRANSFER_FUNDS,
        TRANSFER_FUNDS_ALT,
        APPLY_FOR_LOAN,
        REQUEST_LOAN_INFO,
        AUTHENTICATE_USER,
        VERIFY_IDENTITY,
        VERIFY_IDENTITY_ALT
    )
    
    /**
     * Check if a function name is a banking function
     */
    fun isBankingFunction(functionName: String): Boolean {
        return getAllFunctionNames().contains(functionName)
    }
}
