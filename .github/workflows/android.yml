name: Android CI

on:
  push:
    branches: [ main ]
    paths-ignore:
      - '**.md'
      - 'LICENSE'
      - 'NOTICE'
      - '.gitignore'
  pull_request:
    branches: [ main ]
    paths-ignore:
      - '**.md'
      - 'LICENSE'
      - 'NOTICE'
      - '.gitignore'

jobs:
  build:
    runs-on: ubuntu-latest
    defaults:
      run:
        working-directory: ./components-android
    steps:
    - name: checkout components-android
      uses: actions/checkout@v4
      with:
        path: ./components-android
        submodules: recursive

    - name: set up JDK 17
      uses: actions/setup-java@v3.12.0
      with:
        java-version: '17'
        distribution: 'adopt'

    - uses: actions/cache@v4
      with:
        path: |
          ~/.gradle/caches
          ~/.gradle/wrapper
        key: ${{ runner.os }}-${{ hashFiles('**/*.gradle*') }}-${{ hashFiles('**/gradle/wrapper/gradle-wrapper.properties') }}

    - name: Grant execute permission for gradlew
      run: chmod +x gradlew

    - name: Gradle clean
      run: ./gradlew clean

    - name: Build with Gradle
      run: ./gradlew assembleRelease
