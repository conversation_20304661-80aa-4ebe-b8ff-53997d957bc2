package io.livekit.android.example.voiceassistant.utils

import android.util.Log

/**
 * Custom logger for BankAssist application
 * Formats logs as: BankAssist | ClassName.methodName | message
 */
object BankAssistLogger {
    private const val APP_NAME = "BankAssist"
    
    /**
     * Get the calling method information from the real source (not BankAssistLogger)
     */
    private fun getCallerInfo(): String {
        val stackTrace = Thread.currentThread().stackTrace

        // Find the first stack element that's not from BankAssistLogger or extension functions
        for (i in 3 until stackTrace.size) {
            val element = stackTrace[i]
            val className = element.className

            // Skip BankAssistLogger class and extension functions
            if (!className.contains("BankAssistLogger") &&
                !className.contains("BankAssistLoggerKt") &&
                !element.methodName.startsWith("log") ||
                (element.methodName.startsWith("log") && !className.contains("BankAssistLogger"))) {

                val simpleClassName = className.substringAfterLast('.')
                val methodName = element.methodName

                return "$simpleClassName.$methodName"
            }
        }

        // Fallback if we can't find a suitable caller
        return "Unknown.unknown"
    }
    
    /**
     * Format the log message with app name and caller info
     */
    private fun formatMessage(message: String): String {
        val callerInfo = getCallerInfo()
        return "$APP_NAME | $callerInfo | $message"
    }
    
    /**
     * Log debug message
     */
    fun d(message: String, tag: String = APP_NAME) {
        Log.d(tag, formatMessage(message))
    }
    
    /**
     * Log debug message with throwable
     */
    fun d(message: String, throwable: Throwable, tag: String = APP_NAME) {
        Log.d(tag, formatMessage(message), throwable)
    }
    
    /**
     * Log info message
     */
    fun i(message: String, tag: String = APP_NAME) {
        Log.i(tag, formatMessage(message))
    }
    
    /**
     * Log info message with throwable
     */
    fun i(message: String, throwable: Throwable, tag: String = APP_NAME) {
        Log.i(tag, formatMessage(message), throwable)
    }
    
    /**
     * Log warning message
     */
    fun w(message: String, tag: String = APP_NAME) {
        Log.w(tag, formatMessage(message))
    }
    
    /**
     * Log warning message with throwable
     */
    fun w(message: String, throwable: Throwable, tag: String = APP_NAME) {
        Log.w(tag, formatMessage(message), throwable)
    }
    
    /**
     * Log error message
     */
    fun e(message: String, tag: String = APP_NAME) {
        Log.e(tag, formatMessage(message))
    }
    
    /**
     * Log error message with throwable
     */
    fun e(message: String, throwable: Throwable, tag: String = APP_NAME) {
        Log.e(tag, formatMessage(message), throwable)
    }
    
    /**
     * Log verbose message
     */
    fun v(message: String, tag: String = APP_NAME) {
        Log.v(tag, formatMessage(message))
    }
    
    /**
     * Log verbose message with throwable
     */
    fun v(message: String, throwable: Throwable, tag: String = APP_NAME) {
        Log.v(tag, formatMessage(message), throwable)
    }
    
    /**
     * Log what a user said (special formatting for voice input)
     */
    fun logUserSpeech(message: String) {
        i("USER SPEECH: $message")
    }
    
    /**
     * Log what the agent responded (special formatting for agent responses)
     */
    fun logAgentResponse(message: String) {
        i("AGENT RESPONSE: $message")
    }
    
    /**
     * Log banking operations (special formatting for banking actions)
     */
    fun logBankingOperation(operation: String, details: String = "") {
        i("BANKING OP: $operation${if (details.isNotEmpty()) " - $details" else ""}")
    }
    
    /**
     * Log RPC calls (special formatting for RPC operations)
     */
    fun logRpcCall(method: String, participant: String, status: String) {
        i("RPC CALL: $method to $participant - $status")
    }
    
    /**
     * Log wake word detection events
     */
    fun logWakeWord(event: String) {
        i("WAKE WORD: $event")
    }
    
    /**
     * Log voice assistant overlay events
     */
    fun logVoiceOverlay(event: String) {
        i("VOICE OVERLAY: $event")
    }

    /**
     * Log server events from incoming data streams
     */
    fun logServerEvent(eventData: String, participantId: String = "unknown", dataSize: Int = 0) {
        i("SERVER EVENT: [${participantId}] (${dataSize} bytes) -> $eventData")
    }

    /**
     * Log server event with raw byte array (will decode to UTF-8)
     */
    fun logServerEvent(byteArray: ByteArray, participantId: String = "unknown") {
        try {
            val decodedText = String(byteArray, Charsets.UTF_8)
            logServerEvent(decodedText, participantId, byteArray.size)
        } catch (e: Exception) {
            e("Failed to decode server event data as UTF-8: ${e.message}")
            // Log as hex string fallback
            val hexString = byteArray.joinToString(" ") { "%02x".format(it) }
            i("SERVER EVENT: [${participantId}] (${byteArray.size} bytes) -> HEX: $hexString")
        }
    }
}

/**
 * Extension functions for easier usage
 */
fun Any.logD(message: String) = BankAssistLogger.d(message)
fun Any.logI(message: String) = BankAssistLogger.i(message)
fun Any.logW(message: String) = BankAssistLogger.w(message)
fun Any.logE(message: String) = BankAssistLogger.e(message)
fun Any.logV(message: String) = BankAssistLogger.v(message)

fun Any.logD(message: String, throwable: Throwable) = BankAssistLogger.d(message, throwable)
fun Any.logI(message: String, throwable: Throwable) = BankAssistLogger.i(message, throwable)
fun Any.logW(message: String, throwable: Throwable) = BankAssistLogger.w(message, throwable)
fun Any.logE(message: String, throwable: Throwable) = BankAssistLogger.e(message, throwable)
fun Any.logV(message: String, throwable: Throwable) = BankAssistLogger.v(message, throwable)
