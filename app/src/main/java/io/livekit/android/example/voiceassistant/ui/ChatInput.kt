package io.livekit.android.example.voiceassistant.ui

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.Send
import androidx.compose.material.icons.filled.Keyboard
import androidx.compose.material.icons.filled.Mic
import androidx.compose.material.icons.filled.MicOff
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedTextField
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalSoftwareKeyboardController
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.input.KeyboardCapitalization
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp

/**
 * Enhanced chat input component with voice and text input capabilities
 */
@Composable
fun ChatInput(
    onSendMessage: (String) -> Unit,
    modifier: Modifier = Modifier,
    placeholder: String = "Type a message or use voice...",
    isVoiceActive: Boolean = false,
    onVoiceToggle: (() -> Unit)? = null,
    voiceTranscription: String? = null
) {
    var text by remember { mutableStateOf("") }
    var inputMode by remember { mutableStateOf(InputMode.TEXT) }
    val keyboardController = LocalSoftwareKeyboardController.current

    // Update text field with voice transcription
    if (voiceTranscription != null && inputMode == InputMode.VOICE) {
        text = voiceTranscription
    }

    val sendMessage = {
        if (text.isNotBlank()) {
            onSendMessage(text.trim())
            text = ""
            keyboardController?.hide()
        }
    }

    Surface(
        modifier = modifier,
        color = MaterialTheme.colorScheme.surface,
        shadowElevation = 8.dp
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp)
        ) {
            // Voice status indicator
            if (isVoiceActive) {
                Card(
                    modifier = Modifier.fillMaxWidth(),
                    colors = CardDefaults.cardColors(
                        containerColor = MaterialTheme.colorScheme.primaryContainer
                    )
                ) {
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(12.dp),
                        horizontalArrangement = Arrangement.Center,
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Icon(
                            imageVector = Icons.Default.Mic,
                            contentDescription = null,
                            modifier = Modifier.size(16.dp),
                            tint = MaterialTheme.colorScheme.onPrimaryContainer
                        )
                        Spacer(modifier = Modifier.size(8.dp))
                        Text(
                            text = "🎤 Listening...",
                            style = MaterialTheme.typography.bodySmall,
                            fontWeight = FontWeight.Medium,
                            color = MaterialTheme.colorScheme.onPrimaryContainer
                        )
                    }
                }
                Spacer(modifier = Modifier.height(8.dp))
            }

            // Input controls
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(8.dp),
                verticalAlignment = Alignment.Bottom
            ) {
                // Input mode toggle
                IconButton(
                    onClick = {
                        inputMode = if (inputMode == InputMode.TEXT) InputMode.VOICE else InputMode.TEXT
                        if (inputMode == InputMode.VOICE) {
                            onVoiceToggle?.invoke()
                        }
                    }
                ) {
                    Icon(
                        imageVector = when (inputMode) {
                            InputMode.TEXT -> Icons.Default.Mic
                            InputMode.VOICE -> Icons.Default.Keyboard
                        },
                        contentDescription = when (inputMode) {
                            InputMode.TEXT -> "Switch to voice input"
                            InputMode.VOICE -> "Switch to text input"
                        },
                        tint = if (inputMode == InputMode.VOICE && isVoiceActive) {
                            MaterialTheme.colorScheme.primary
                        } else {
                            MaterialTheme.colorScheme.onSurface
                        }
                    )
                }

                // Text input field
                OutlinedTextField(
                    value = text,
                    onValueChange = {
                        text = it
                        if (inputMode == InputMode.VOICE) {
                            inputMode = InputMode.TEXT
                        }
                    },
                    modifier = Modifier.weight(1f),
                    placeholder = {
                        Text(
                            when (inputMode) {
                                InputMode.TEXT -> "Type a message..."
                                InputMode.VOICE -> if (isVoiceActive) "Listening..." else "Tap mic to speak"
                            }
                        )
                    },
                    shape = RoundedCornerShape(24.dp),
                    keyboardOptions = KeyboardOptions(
                        capitalization = KeyboardCapitalization.Sentences,
                        imeAction = ImeAction.Send
                    ),
                    keyboardActions = KeyboardActions(
                        onSend = { sendMessage() }
                    ),
                    maxLines = 4,
                    readOnly = inputMode == InputMode.VOICE && isVoiceActive
                )

                // Send button
                IconButton(
                    onClick = sendMessage,
                    enabled = text.isNotBlank()
                ) {
                    Icon(
                        imageVector = Icons.AutoMirrored.Filled.Send,
                        contentDescription = "Send message",
                        tint = if (text.isNotBlank()) {
                            MaterialTheme.colorScheme.primary
                        } else {
                            MaterialTheme.colorScheme.onSurface.copy(alpha = 0.38f)
                        }
                    )
                }
            }

            // Banking quick actions
            if (text.isNotBlank()) {
                val suggestions = getBankingQuickActions(text)
                if (suggestions.isNotEmpty()) {
                    Spacer(modifier = Modifier.height(8.dp))
                    Card(
                        modifier = Modifier.fillMaxWidth(),
                        colors = CardDefaults.cardColors(
                            containerColor = MaterialTheme.colorScheme.surfaceVariant
                        )
                    ) {
                        Column(modifier = Modifier.padding(12.dp)) {
                            Text(
                                text = "💡 Banking Suggestions:",
                                style = MaterialTheme.typography.labelMedium,
                                fontWeight = FontWeight.SemiBold
                            )
                            Text(
                                text = suggestions.joinToString(" • "),
                                style = MaterialTheme.typography.bodySmall,
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                        }
                    }
                }
            }
        }
    }
}

/**
 * Input mode for the chat interface
 */
enum class InputMode {
    TEXT,
    VOICE
}

/**
 * Get banking-related quick action suggestions based on user input
 */
private fun getBankingQuickActions(text: String): List<String> {
    val lowerText = text.lowercase()
    val suggestions = mutableListOf<String>()

    when {
        lowerText.contains("balance") -> suggestions.add("Check account balance")
        lowerText.contains("transfer") || lowerText.contains("send") -> suggestions.add("Transfer funds")
        lowerText.contains("loan") || lowerText.contains("borrow") -> suggestions.add("Apply for loan")
        lowerText.contains("history") || lowerText.contains("transactions") -> suggestions.add("View transaction history")
        lowerText.contains("account") && lowerText.contains("open") -> suggestions.add("Open new account")
        lowerText.contains("verify") || lowerText.contains("authenticate") -> suggestions.add("Verify identity")
        lowerText.contains("help") -> suggestions.addAll(listOf("Check balance", "Transfer funds", "View history"))
    }

    return suggestions.take(3) // Limit to 3 suggestions
}

@Preview(showBackground = true)
@Composable
fun ChatInputPreview() {
    MaterialTheme {
        ChatInput(
            onSendMessage = { /* Preview - no action */ },
            placeholder = "Type your message here...",
            isVoiceActive = false,
            onVoiceToggle = { /* Preview - no action */ }
        )
    }
}

@Preview(showBackground = true)
@Composable
fun ChatInputVoiceActivePreview() {
    MaterialTheme {
        ChatInput(
            onSendMessage = { /* Preview - no action */ },
            placeholder = "Listening...",
            isVoiceActive = true,
            onVoiceToggle = { /* Preview - no action */ },
            voiceTranscription = "Check my account balance"
        )
    }
}
