package io.livekit.android.example.voiceassistant.managers

import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import io.livekit.android.example.voiceassistant.utils.BankAssistLogger
import io.livekit.android.room.Room
import io.livekit.android.room.participant.Participant
import kotlinx.coroutines.launch
import kotlinx.serialization.Serializable
import kotlinx.serialization.json.Json
import kotlinx.serialization.encodeToString
import kotlinx.serialization.decodeFromString
import java.io.File

/**
 * Data classes for different stream types
 */
@Serializable
data class TextStreamInfo(
    val id: String,
    val topic: String,
    val sender: String,
    val timestamp: Long,
    val size: Long? = null
)

@Serializable
data class FileStreamInfo(
    val id: String,
    val topic: String,
    val sender: String,
    val fileName: String,
    val mimeType: String,
    val size: Long,
    val timestamp: Long
)

@Serializable
data class StructuredData(
    val type: String,
    val data: Map<String, String>, // Changed from Any to String for serialization
    val timestamp: Long
)

/**
 * Stream progress information
 */
data class StreamProgress(
    val streamId: String,
    val bytesTransferred: Long,
    val totalBytes: Long,
    val progress: Float
) {
    val isComplete: Boolean get() = progress >= 1.0f
}

/**
 * Stream event for UI updates
 */
sealed class StreamEvent {
    data class TextReceived(
        val info: TextStreamInfo,
        val content: String,
        val participant: Participant.Identity
    ) : StreamEvent()
    
    data class FileReceived(
        val info: FileStreamInfo,
        val file: File,
        val participant: Participant.Identity
    ) : StreamEvent()
    
    data class StructuredDataReceived(
        val data: StructuredData,
        val participant: Participant.Identity
    ) : StreamEvent()
    
    data class ProgressUpdate(
        val progress: StreamProgress
    ) : StreamEvent()
    
    data class StreamError(
        val streamId: String,
        val error: String,
        val participant: Participant.Identity?
    ) : StreamEvent()
}

/**
 * Manages enhanced data streaming functionality for LiveKit
 */
class DataStreamManager(
    private val room: Room,
    private val json: Json = Json { ignoreUnknownKeys = true }
) {
    private val _streamEvents = mutableStateListOf<StreamEvent>()
    val streamEvents: List<StreamEvent> = _streamEvents
    
    private val _activeStreams = mutableMapOf<String, StreamProgress>()
    val activeStreams: Map<String, StreamProgress> = _activeStreams

    companion object {
        const val TOPIC_CHAT = "lk.chat"
        const val TOPIC_FILE_TRANSFER = "lk.file"
        const val TOPIC_STRUCTURED_DATA = "lk.data"
        const val TOPIC_SYSTEM = "lk.system"
    }

    /**
     * Register stream handlers for different topics (simplified for Android SDK)
     */
    fun registerStreamHandlers() {
        try {
            // Register text stream handler for chat (using existing API)
            room.registerTextStreamHandler(TOPIC_CHAT) { receiver, identity ->
                // Launch coroutine to handle suspend function
                kotlinx.coroutines.GlobalScope.launch {
                    handleTextStreamSimplified(receiver, identity, TOPIC_CHAT)
                }
            }

            // Register data received handler for server events
            registerDataReceivedHandler()

            BankAssistLogger.i("Successfully registered stream handlers")
        } catch (e: Exception) {
            BankAssistLogger.e("Failed to register stream handlers", e)
        }
    }

    /**
     * Register handler for incoming data events from server
     * Note: Primary data handling is now done by EventManager using room.events.collect
     */
    private fun registerDataReceivedHandler() {
        try {
            // Data received events are now handled by EventManager using the proper LiveKit event system
            // This method is kept for compatibility but the real handling happens in EventManager
            BankAssistLogger.i("Data received events are handled by EventManager via room.events.collect")
            BankAssistLogger.d("Current remote participants: ${room.remoteParticipants.size}")

            room.remoteParticipants.values.forEach { participant ->
                BankAssistLogger.d("Remote participant: ${participant.identity}")
            }
        } catch (e: Exception) {
            BankAssistLogger.e("Failed to register data received handlers", e)
        }
    }

    /**
     * Handle incoming data from server participants
     * Note: This method can be called by EventManager for stream-specific processing
     */
    fun handleDataReceived(participant: Participant, data: ByteArray) {
        try {
            BankAssistLogger.logServerEvent(data, participant.identity?.value ?: "unknown")

            // Try to parse as different data types
            val dataString = String(data, Charsets.UTF_8)

            when {
                // Check if it's JSON structured data
                dataString.trim().startsWith("{") && dataString.trim().endsWith("}") -> {
                    try {
                        val structuredData = json.decodeFromString<StructuredData>(dataString)
                        addStreamEvent(StreamEvent.StructuredDataReceived(
                            data = structuredData,
                            participant = participant.identity ?: Participant.Identity("unknown")
                        ))
                        BankAssistLogger.i("Parsed structured data: ${structuredData.type}")
                    } catch (e: Exception) {
                        BankAssistLogger.w("Failed to parse as structured data: ${e.message}")
                        handleRawTextData(participant, dataString)
                    }
                }

                // Check if it's plain text
                dataString.isNotBlank() -> {
                    handleRawTextData(participant, dataString)
                }

                // Handle binary data
                else -> {
                    BankAssistLogger.i("Received binary data from ${participant.identity}: ${data.size} bytes")
                    // Could be a file or other binary content
                }
            }

        } catch (e: Exception) {
            BankAssistLogger.e("Error handling received data from ${participant.identity}", e)
            addStreamEvent(StreamEvent.StreamError(
                streamId = "data_${System.currentTimeMillis()}",
                error = "Failed to process received data: ${e.message}",
                participant = participant.identity
            ))
        }
    }

    /**
     * Handle raw text data from server
     */
    private fun handleRawTextData(participant: Participant, text: String) {
        val info = TextStreamInfo(
            id = "received_${System.currentTimeMillis()}",
            topic = TOPIC_SYSTEM,
            sender = participant.identity?.value ?: "unknown",
            timestamp = System.currentTimeMillis(),
            size = text.length.toLong()
        )

        addStreamEvent(StreamEvent.TextReceived(
            info = info,
            content = text,
            participant = participant.identity ?: Participant.Identity("unknown")
        ))

        BankAssistLogger.i("Processed text data from ${participant.identity}: ${text.take(100)}...")
    }

    /**
     * Send a text message using text streams (simplified for Android SDK)
     */
    suspend fun sendTextMessage(text: String, topic: String = TOPIC_CHAT): String? {
        return try {
            // For now, use the existing publishData method as fallback
            // In a full implementation, you would use the text streaming API when available
            room.localParticipant.publishData(text.toByteArray())

            val streamId = "text_${System.currentTimeMillis()}"
            BankAssistLogger.d("Sent text message on topic $topic: ${text.take(50)}...")
            streamId

        } catch (e: Exception) {
            BankAssistLogger.e("Failed to send text message: ${e.message}", e)
            addStreamEvent(StreamEvent.StreamError(
                streamId = "unknown",
                error = e.message ?: "Unknown error",
                participant = room.localParticipant.identity
            ))
            null
        }
    }

    /**
     * Send structured data as JSON (simplified for Android SDK)
     */
    suspend fun sendStructuredData(data: StructuredData): String? {
        return try {
            val jsonData = json.encodeToString(data)
            // Use publishData as fallback
            room.localParticipant.publishData(jsonData.toByteArray())

            val streamId = "data_${System.currentTimeMillis()}"
            BankAssistLogger.d("Sent structured data: ${data.type}")
            streamId

        } catch (e: Exception) {
            BankAssistLogger.e("Failed to send structured data: ${e.message}", e)
            addStreamEvent(StreamEvent.StreamError(
                streamId = "unknown",
                error = e.message ?: "Unknown error",
                participant = room.localParticipant.identity
            ))
            null
        }
    }

    /**
     * Send a file using data publishing (simplified for Android SDK)
     */
    suspend fun sendFile(file: File, mimeType: String = "application/octet-stream"): String? {
        return try {
            // Read file and send via publishData
            val fileBytes = file.readBytes()
            room.localParticipant.publishData(fileBytes)

            val streamId = "file_${System.currentTimeMillis()}"
            BankAssistLogger.d("Sent file ${file.name} (${fileBytes.size} bytes)")
            streamId

        } catch (e: Exception) {
            BankAssistLogger.e("Failed to send file ${file.name}: ${e.message}", e)
            addStreamEvent(StreamEvent.StreamError(
                streamId = "unknown",
                error = e.message ?: "Unknown error",
                participant = room.localParticipant.identity
            ))
            null
        }
    }

    /**
     * Stream bytes incrementally (simplified for Android SDK)
     */
    suspend fun streamBytes(topic: String, data: ByteArray): String? {
        return try {
            // Use publishData for byte streaming
            room.localParticipant.publishData(data)

            val streamId = "bytes_${System.currentTimeMillis()}"
            BankAssistLogger.d("Streamed ${data.size} bytes on topic $topic")
            streamId

        } catch (e: Exception) {
            BankAssistLogger.e("Failed to stream bytes: ${e.message}", e)
            addStreamEvent(StreamEvent.StreamError(
                streamId = "unknown",
                error = e.message ?: "Unknown error",
                participant = room.localParticipant.identity
            ))
            null
        }
    }

    /**
     * Handle incoming text streams (simplified for Android SDK)
     */
    private suspend fun handleTextStreamSimplified(
        receiver: Any, // TextStreamReceiver type from LiveKit
        identity: Participant.Identity,
        topic: String
    ) {
        try {
            BankAssistLogger.d("Receiving text stream from $identity on topic $topic")

            // This is a placeholder for the actual text stream handling
            // In the current implementation, we'll use the existing chat message handling
            val info = TextStreamInfo(
                id = "stream_${System.currentTimeMillis()}",
                topic = topic,
                sender = identity.value,
                timestamp = System.currentTimeMillis()
            )

            addStreamEvent(StreamEvent.TextReceived(
                info = info,
                content = "Text stream received", // Placeholder
                participant = identity
            ))

        } catch (e: Exception) {
            BankAssistLogger.e("Error handling text stream: ${e.message}", e)
            addStreamEvent(StreamEvent.StreamError(
                streamId = "unknown",
                error = e.message ?: "Unknown error",
                participant = identity
            ))
        }
    }



    private fun addStreamEvent(event: StreamEvent) {
        _streamEvents.add(event)
        
        // Keep only the last 100 events to prevent memory issues
        if (_streamEvents.size > 100) {
            _streamEvents.removeAt(0)
        }
    }

    private fun updateStreamProgress(streamId: String, totalBytes: Long, bytesTransferred: Long) {
        val progress = if (totalBytes > 0) bytesTransferred.toFloat() / totalBytes else 0f
        val streamProgress = StreamProgress(streamId, bytesTransferred, totalBytes, progress)
        
        _activeStreams[streamId] = streamProgress
        addStreamEvent(StreamEvent.ProgressUpdate(streamProgress))
        
        // Remove completed streams after a delay
        if (streamProgress.isComplete) {
            // In a real app, you might want to remove this after some time
        }
    }

    fun cleanup() {
        try {
            // Unregister stream handlers
            room.unregisterTextStreamHandler(TOPIC_CHAT)

            _streamEvents.clear()
            _activeStreams.clear()

            BankAssistLogger.d("Cleaned up data stream manager")
        } catch (e: Exception) {
            BankAssistLogger.e("Error during data stream manager cleanup", e)
        }
    }
}

/**
 * Composable function to create and manage DataStreamManager
 */
@Composable
fun rememberDataStreamManager(room: Room): DataStreamManager {
    val coroutineScope = rememberCoroutineScope()
    
    val dataStreamManager = remember(room) {
        DataStreamManager(room).apply {
            coroutineScope.launch {
                registerStreamHandlers()
            }
        }
    }

    DisposableEffect(room) {
        onDispose {
            dataStreamManager.cleanup()
        }
    }

    return dataStreamManager
}
