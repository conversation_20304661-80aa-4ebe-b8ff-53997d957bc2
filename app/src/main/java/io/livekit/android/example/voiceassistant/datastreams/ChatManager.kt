package io.livekit.android.example.voiceassistant.datastreams

import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import com.github.ajalt.timberkt.Timber
import io.livekit.android.room.Room
import io.livekit.android.room.participant.Participant
import kotlinx.coroutines.launch

private const val CHAT_TOPIC = "lk.chat"

/**
 * Manages both transcriptions and text chat messages
 */
@Composable
fun rememberChatManager(room: Room): ChatManager {
    val coroutineScope = rememberCoroutineScope()
    val messages = remember(room) { mutableStateListOf<ChatMessage>() }
    val transcriptions = rememberTranscriptions(room)

    // Convert transcriptions to chat messages and merge with text messages
    val allMessages = remember(transcriptions, messages) {
        derivedStateOf {
            val transcriptionMessages = transcriptions.map { it.toChatMessage() }
            val allChatMessages = (transcriptionMessages + messages).sortedBy { it.timestamp }
            Timber.d { "Total messages: transcriptions=${transcriptionMessages.size}, text=${messages.size}, combined=${allChatMessages.size}" }
            allChatMessages
        }
    }

    val chatManager = remember(room) {
        ChatManager(
            room = room,
            messages = allMessages.value,
            onSendTextMessage = { text ->
                coroutineScope.launch {
                    try {
                        // Add to local messages immediately for better UX
                        val textMessage = ChatMessage.TextMessage(
                            identity = room.localParticipant.identity ?: Participant.Identity("user"),
                            text = text,
                            isFromUser = true
                        )
                        messages.add(textMessage)

                        // Try to send via LiveKit text stream (similar to transcriptions)
                        try {
                            // For now, we'll use publishData as a fallback
                            // In a full implementation, you would set up a text stream publisher
                            val messageBytes = text.toByteArray(Charsets.UTF_8)
                            room.localParticipant.publishData(messageBytes)

                            Timber.d { "Successfully sent text message via LiveKit: $text" }

                        } catch (dataException: Exception) {
                            Timber.w(dataException) { "Failed to send via LiveKit, message added locally only: ${dataException.message}" }
                            // Note: The message is still added locally above for immediate feedback
                        }

                    } catch (e: Exception) {
                        Timber.e(e) { "Failed to add text message: ${e.message}" }
                    }
                }
            }
        )
    }

    // Listen for incoming chat messages using a similar pattern to transcriptions
    DisposableEffect(room) {
        // Use the same text stream handler pattern as transcriptions but for chat
        room.registerTextStreamHandler(CHAT_TOPIC) { receiver, identity ->
            coroutineScope.launch {
                try {
                    Timber.d { "Registered chat message handler for participant: $identity" }

                    // Collect the incoming chat message stream
                    receiver.flow.collect { messageText ->
                        val participantIdentity = identity
                        val isFromLocalUser = participantIdentity == room.localParticipant.identity

                        // Only add if it's not from the local participant (we already added it above)
                        if (!isFromLocalUser) {
                            val textMessage = ChatMessage.TextMessage(
                                identity = participantIdentity,
                                text = messageText,
                                isFromUser = false
                            )
                            messages.add(textMessage)
                            Timber.d { "Received and added text message from $participantIdentity: $messageText" }
                        } else {
                            Timber.d { "Ignoring message from local participant: $messageText" }
                        }
                    }
                } catch (e: Exception) {
                    Timber.e(e) { "Failed to process received chat message from $identity" }
                }
            }
        }

        onDispose {
            // Clean up the handler when done with it
            room.unregisterTextStreamHandler(CHAT_TOPIC)
            Timber.d { "Unregistered chat message handler" }
        }
    }

    // Update chat manager with latest messages and add debugging
    val updatedChatManager = chatManager.copy(messages = allMessages.value)

    // Debug logging for chat manager state
    LaunchedEffect(allMessages.value.size) {
        Timber.d { "ChatManager updated - Total messages: ${allMessages.value.size}" }
        allMessages.value.forEachIndexed { index, message ->
            Timber.d { "Message $index: ${message.identity} -> ${message.text.take(50)}..." }
        }
    }

    return updatedChatManager
}

/**
 * Chat manager that handles both transcriptions and text messages
 */
data class ChatManager(
    val room: Room,
    val messages: List<ChatMessage>,
    val onSendTextMessage: (String) -> Unit
) {
    /**
     * Get the last user message (either transcription or text)
     */
    val lastUserMessage: ChatMessage?
        get() = messages.lastOrNull { it.identity == (room.localParticipant.identity ?: Participant.Identity("user")) }

    /**
     * Get the last agent message (either transcription or text)
     */
    val lastAgentMessage: ChatMessage?
        get() = messages.lastOrNull { it.identity != (room.localParticipant.identity ?: Participant.Identity("user")) }

    /**
     * Get messages for display (showing all messages chronologically)
     */
    val displayMessages: List<ChatMessage>
        get() = allMessages

    /**
     * Get all messages chronologically
     */
    val allMessages: List<ChatMessage>
        get() = messages.sortedBy { it.timestamp }
}
