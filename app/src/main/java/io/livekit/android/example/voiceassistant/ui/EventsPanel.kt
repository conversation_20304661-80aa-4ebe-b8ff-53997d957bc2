package io.livekit.android.example.voiceassistant.ui

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.AccountBalance
import androidx.compose.material.icons.filled.Add
import androidx.compose.material.icons.filled.Check
import androidx.compose.material.icons.filled.Clear
import androidx.compose.material.icons.filled.Close
import androidx.compose.material.icons.filled.Edit
import androidx.compose.material.icons.filled.Error
import androidx.compose.material.icons.filled.Info
import androidx.compose.material.icons.filled.KeyboardArrowDown
import androidx.compose.material.icons.filled.KeyboardArrowUp
import androidx.compose.material.icons.filled.Person
import androidx.compose.material.icons.filled.PlayArrow
import androidx.compose.material.icons.filled.Stop
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import io.livekit.android.example.voiceassistant.managers.*

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun EventsPanel(
    eventManager: EventManager,
    modifier: Modifier = Modifier
) {
    var showStats by remember { mutableStateOf(false) }

    Column(modifier = modifier.padding(16.dp)) {
        // Header
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = "LiveKit Events",
                style = MaterialTheme.typography.headlineSmall,
                fontWeight = FontWeight.Bold
            )
            
            Row {
                IconButton(onClick = { showStats = !showStats }) {
                    Icon(
                        if (showStats) Icons.Default.KeyboardArrowUp else Icons.Default.KeyboardArrowDown,
                        contentDescription = "Toggle Stats"
                    )
                }
                
                IconButton(onClick = { eventManager.clearOldEvents(0) }) {
                    Icon(Icons.Default.Clear, contentDescription = "Clear Events")
                }
            }
        }

        // Room Statistics (collapsible)
        if (showStats) {
            Spacer(modifier = Modifier.height(8.dp))
            RoomStatsCard(eventManager = eventManager)
            Spacer(modifier = Modifier.height(8.dp))
        }

        // Participants section
        val participants = eventManager.participants
        if (participants.isNotEmpty()) {
            Text(
                text = "Participants (${participants.size}):",
                style = MaterialTheme.typography.bodyMedium,
                fontWeight = FontWeight.Medium
            )
            
            Spacer(modifier = Modifier.height(8.dp))
            
            LazyColumn(
                modifier = Modifier.height(120.dp),
                verticalArrangement = Arrangement.spacedBy(4.dp)
            ) {
                items(participants) { participant ->
                    ParticipantEventCard(participant = participant)
                }
            }
            
            Spacer(modifier = Modifier.height(16.dp))
        }

        // Events list
        Text(
            text = "Recent Events:",
            style = MaterialTheme.typography.bodyMedium,
            fontWeight = FontWeight.Medium
        )
        
        Spacer(modifier = Modifier.height(8.dp))
        
        LazyColumn(
            modifier = Modifier.fillMaxHeight(),
            verticalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            items(eventManager.events.takeLast(50).reversed()) { event ->
                EventCard(event = event)
            }
            
            if (eventManager.events.isEmpty()) {
                item {
                    Card(
                        modifier = Modifier.fillMaxWidth(),
                        colors = CardDefaults.cardColors(containerColor = MaterialTheme.colorScheme.surfaceVariant)
                    ) {
                        Box(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(16.dp),
                            contentAlignment = Alignment.Center
                        ) {
                            Text(
                                text = "No events yet",
                                style = MaterialTheme.typography.bodyMedium,
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                        }
                    }
                }
            }
        }
    }
}

@Composable
fun RoomStatsCard(
    eventManager: EventManager,
    modifier: Modifier = Modifier
) {
    val stats = eventManager.getRoomStats()
    
    Card(
        modifier = modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(containerColor = MaterialTheme.colorScheme.primaryContainer)
    ) {
        Column(
            modifier = Modifier.padding(12.dp)
        ) {
            Text(
                text = "Room Statistics",
                style = MaterialTheme.typography.bodyMedium,
                fontWeight = FontWeight.Medium,
                color = MaterialTheme.colorScheme.onPrimaryContainer
            )
            
            Spacer(modifier = Modifier.height(8.dp))
            
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Column {
                    StatItem("Participants", stats["participantCount"].toString())
                    StatItem("Events", stats["eventCount"].toString())
                }
                
                Column {
                    StatItem("Connection", stats["connectionState"].toString())
                    StatItem("Room", stats["roomName"].toString())
                }
            }
        }
    }
}

@Composable
fun StatItem(
    label: String,
    value: String,
    modifier: Modifier = Modifier
) {
    Column(modifier = modifier) {
        Text(
            text = label,
            style = MaterialTheme.typography.labelSmall,
            color = MaterialTheme.colorScheme.onPrimaryContainer.copy(alpha = 0.7f)
        )
        Text(
            text = value,
            style = MaterialTheme.typography.bodySmall,
            fontWeight = FontWeight.Medium,
            color = MaterialTheme.colorScheme.onPrimaryContainer
        )
    }
}

@Composable
fun ParticipantEventCard(
    participant: io.livekit.android.room.participant.Participant,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(containerColor = MaterialTheme.colorScheme.surface)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(12.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Icon(
                Icons.Default.Person,
                contentDescription = null,
                tint = MaterialTheme.colorScheme.primary
            )
            
            Spacer(modifier = Modifier.width(8.dp))
            
            Column(modifier = Modifier.weight(1f)) {
                Text(
                    text = participant.identity?.value ?: "Unknown",
                    style = MaterialTheme.typography.bodyMedium,
                    fontWeight = FontWeight.Medium
                )
                
                Text(
                    text = "Connection: Connected", // Simplified
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
            
            // Connection quality indicator
            Icon(
                Icons.Default.Check,
                contentDescription = "Connection Quality",
                tint = Color.Green,
                modifier = Modifier.size(16.dp)
            )
        }
    }
}

@Composable
fun EventCard(
    event: LiveKitEvent,
    modifier: Modifier = Modifier
) {
    val (icon, color, title, description) = getEventDisplayInfo(event)
    
    Card(
        modifier = modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(containerColor = MaterialTheme.colorScheme.surfaceVariant)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(12.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Icon(
                icon,
                contentDescription = null,
                tint = color,
                modifier = Modifier.size(20.dp)
            )
            
            Spacer(modifier = Modifier.width(12.dp))
            
            Column(modifier = Modifier.weight(1f)) {
                Text(
                    text = title,
                    style = MaterialTheme.typography.bodyMedium,
                    fontWeight = FontWeight.Medium
                )
                
                if (description.isNotEmpty()) {
                    Text(
                        text = description,
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
            }
            
            Text(
                text = formatEventTime(event.timestamp),
                style = MaterialTheme.typography.labelSmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }
    }
}

private fun getEventDisplayInfo(event: LiveKitEvent): EventDisplayInfo {
    return when (event) {
        is LiveKitEvent.ParticipantConnected -> EventDisplayInfo(
            icon = Icons.Default.Add,
            color = Color.Green,
            title = "Participant Joined",
            description = event.participant.identity?.value ?: "Unknown"
        )

        is LiveKitEvent.ParticipantDisconnected -> EventDisplayInfo(
            icon = Icons.Default.Close,
            color = Color.Red,
            title = "Participant Left",
            description = event.participant.identity?.value ?: "Unknown"
        )

        is LiveKitEvent.ConnectionStateChanged -> EventDisplayInfo(
            icon = Icons.Default.Check,
            color = Color.Blue,
            title = "Connection State",
            description = event.state
        )

        is LiveKitEvent.TrackPublished -> EventDisplayInfo(
            icon = Icons.Default.PlayArrow,
            color = Color.Green,
            title = "Track Published",
            description = "${event.participant.identity?.value}: ${event.trackType}"
        )

        is LiveKitEvent.TrackUnpublished -> EventDisplayInfo(
            icon = Icons.Default.Stop,
            color = Color(0xFFFF9800), // Orange
            title = "Track Unpublished",
            description = "${event.participant.identity?.value}: ${event.trackType}"
        )

        is LiveKitEvent.DataReceived -> EventDisplayInfo(
            icon = Icons.Default.Info,
            color = Color(0xFF9C27B0), // Purple
            title = "Data Received",
            description = "${event.participant.identity?.value}: ${event.dataSize} bytes"
        )

        is LiveKitEvent.RoomMetadataChanged -> EventDisplayInfo(
            icon = Icons.Default.Info,
            color = Color.Blue,
            title = "Room Metadata",
            description = event.metadata.take(50)
        )

        is LiveKitEvent.ParticipantMetadataChanged -> EventDisplayInfo(
            icon = Icons.Default.Edit,
            color = Color.Blue,
            title = "Participant Metadata",
            description = "${event.participant.identity?.value}: ${event.metadata.take(30)}"
        )

        is LiveKitEvent.ConnectionQualityChanged -> EventDisplayInfo(
            icon = Icons.Default.Check,
            color = Color.Green,
            title = "Connection Quality",
            description = "${event.participant.identity?.value}: ${event.quality}"
        )

        is LiveKitEvent.ActiveSpeakersChanged -> EventDisplayInfo(
            icon = Icons.Default.Person,
            color = Color(0xFFFF9800), // Orange
            title = "Active Speakers",
            description = "${event.speakers.size} speakers"
        )

        is LiveKitEvent.ErrorOccurred -> EventDisplayInfo(
            icon = Icons.Default.Error,
            color = Color.Red,
            title = "Error",
            description = event.error
        )

        is LiveKitEvent.BankingFunctionResult -> EventDisplayInfo(
            icon = Icons.Default.AccountBalance,
            color = Color(0xFF4CAF50), // Green
            title = "Banking Function",
            description = "${event.functionName}: ${event.result::class.simpleName}"
        )
    }
}

private data class EventDisplayInfo(
    val icon: ImageVector,
    val color: Color,
    val title: String,
    val description: String
)

private val LiveKitEvent.timestamp: Long
    get() = when (this) {
        is LiveKitEvent.ParticipantConnected -> timestamp
        is LiveKitEvent.ParticipantDisconnected -> timestamp
        is LiveKitEvent.ConnectionStateChanged -> timestamp
        is LiveKitEvent.TrackPublished -> timestamp
        is LiveKitEvent.TrackUnpublished -> timestamp
        is LiveKitEvent.DataReceived -> timestamp
        is LiveKitEvent.RoomMetadataChanged -> timestamp
        is LiveKitEvent.ParticipantMetadataChanged -> timestamp
        is LiveKitEvent.ConnectionQualityChanged -> timestamp
        is LiveKitEvent.ActiveSpeakersChanged -> timestamp
        is LiveKitEvent.ErrorOccurred -> timestamp
        is LiveKitEvent.BankingFunctionResult -> timestamp
    }

private fun formatEventTime(timestamp: Long): String {
    val now = System.currentTimeMillis()
    val diff = now - timestamp
    
    return when {
        diff < 60_000 -> "now"
        diff < 3600_000 -> "${diff / 60_000}m"
        diff < 86400_000 -> "${diff / 3600_000}h"
        else -> "${diff / 86400_000}d"
    }
}

@Preview(showBackground = true)
@Composable
fun EventsPanelPreview() {
    MaterialTheme {
        Box(
            modifier = Modifier
                .fillMaxSize()
                .padding(16.dp),
            contentAlignment = Alignment.Center
        ) {
            Text("Events Panel Preview")
        }
    }
}
