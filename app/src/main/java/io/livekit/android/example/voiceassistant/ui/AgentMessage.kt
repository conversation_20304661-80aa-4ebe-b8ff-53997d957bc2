package io.livekit.android.example.voiceassistant.ui

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.core.MutableTransitionState
import androidx.compose.animation.fadeIn
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp

/**
 * Composable for displaying agent messages (both transcriptions and text responses)
 */
@Composable
fun AgentMessage(
    text: String,
    modifier: Modifier = Modifier
) {
    val state = remember {
        MutableTransitionState(false).apply {
            // Start the animation immediately.
            targetState = true
        }
    }
    
    AnimatedVisibility(
        visibleState = state,
        enter = fadeIn(),
        modifier = modifier
    ) {
        Box(
            modifier = modifier
                .clip(RoundedCornerShape(2.dp, 8.dp, 8.dp, 8.dp))
                .background(MaterialTheme.colorScheme.secondaryContainer)
        ) {
            Text(
                text = text,
                fontWeight = FontWeight.Light,
                fontSize = 20.sp,
                modifier = Modifier.padding(12.dp),
                color = MaterialTheme.colorScheme.onSecondaryContainer
            )
        }
    }
}

@Preview(showBackground = true)
@Composable
fun AgentMessagePreview() {
    MaterialTheme {
        AgentMessage(
            text = "Hello! I'm your AI assistant. How can I help you today?"
        )
    }
}
