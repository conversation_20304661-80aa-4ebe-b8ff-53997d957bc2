package io.livekit.android.example.voiceassistant.managers

import android.content.Context
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import ai.picovoice.porcupine.Porcupine
import ai.picovoice.porcupine.PorcupineException
import ai.picovoice.porcupine.PorcupineManager
import ai.picovoice.porcupine.PorcupineManagerCallback
import io.livekit.android.example.voiceassistant.utils.BankAssistLogger
import kotlinx.coroutines.launch

/**
 * Wake word detection state
 */
data class WakeWordState(
    val isListening: Boolean = false,
    val isInitialized: Boolean = false,
    val lastDetection: Long? = null,
    val error: String? = null
)

/**
 * Manages wake word detection using Porcupine
 */
class WakeWordManager(
    private val context: Context,
    private val onWakeWordDetected: () -> Unit,
    private val onError: (String) -> Unit
) {
    private var porcupineManager: PorcupineManager? = null
    private var state by mutableStateOf(WakeWordState())
    
    // Porcupine Access Key - Replace with your actual access key from Picovoice Console
    private val accessKey = "YOUR_PICOVOICE_ACCESS_KEY_HERE" // TODO: Replace with actual key
    
    fun getCurrentState(): WakeWordState = state
    
    /**
     * Initialize wake word detection
     */
    suspend fun initialize() {
        try {
            BankAssistLogger.d("Initializing wake word detection...")

            // Create custom keyword for "rubio"
            // Note: For production, you would create a custom .ppn file for "rubio" using Picovoice Console
            // For now, we'll use a built-in keyword as fallback
            porcupineManager = PorcupineManager.Builder()
                .setAccessKey(accessKey)
                .setKeywords(arrayOf(Porcupine.BuiltInKeyword.PICOVOICE)) // Using built-in keyword as fallback
                .setSensitivity(0.7f) // Adjust sensitivity as needed
                .build(context, object : PorcupineManagerCallback {
                    override fun invoke(keywordIndex: Int) {
                        BankAssistLogger.logWakeWord("Wake word detected! Keyword index: $keywordIndex")
                        state = state.copy(lastDetection = System.currentTimeMillis())
                        onWakeWordDetected()
                    }
                })

            state = state.copy(isInitialized = true, error = null)
            BankAssistLogger.d("Wake word detection initialized successfully")

        } catch (e: PorcupineException) {
            val errorMessage = "Failed to initialize wake word detection: ${e.message}"
            BankAssistLogger.e(errorMessage, e)
            state = state.copy(error = errorMessage, isInitialized = false)
            onError(errorMessage)
        }
    }
    
    /**
     * Start listening for wake word
     */
    suspend fun startListening() {
        try {
            if (!state.isInitialized) {
                initialize()
            }
            
            porcupineManager?.start()
            state = state.copy(isListening = true, error = null)
            BankAssistLogger.logWakeWord("Started listening for wake word")

        } catch (e: PorcupineException) {
            val errorMessage = "Failed to start wake word detection: ${e.message}"
            BankAssistLogger.e(errorMessage, e)
            state = state.copy(error = errorMessage, isListening = false)
            onError(errorMessage)
        }
    }

    /**
     * Stop listening for wake word
     */
    suspend fun stopListening() {
        try {
            porcupineManager?.stop()
            state = state.copy(isListening = false)
            BankAssistLogger.logWakeWord("Stopped listening for wake word")

        } catch (e: PorcupineException) {
            val errorMessage = "Failed to stop wake word detection: ${e.message}"
            BankAssistLogger.e(errorMessage, e)
            state = state.copy(error = errorMessage)
            onError(errorMessage)
        }
    }

    /**
     * Clean up resources
     */
    fun cleanup() {
        try {
            porcupineManager?.delete()
            porcupineManager = null
            state = WakeWordState()
            BankAssistLogger.d("Wake word manager cleaned up")
        } catch (e: Exception) {
            BankAssistLogger.e("Error during wake word manager cleanup", e)
        }
    }
}

/**
 * Composable function to create and manage WakeWordManager
 */
@Composable
fun rememberWakeWordManager(
    context: Context,
    onWakeWordDetected: () -> Unit,
    onError: (String) -> Unit = {}
): WakeWordManager {
    val coroutineScope = rememberCoroutineScope()
    
    val wakeWordManager = remember(context) {
        WakeWordManager(
            context = context,
            onWakeWordDetected = onWakeWordDetected,
            onError = onError
        )
    }
    
    DisposableEffect(wakeWordManager) {
        // Initialize wake word detection when the manager is created
        coroutineScope.launch {
            wakeWordManager.initialize()
        }
        
        onDispose {
            wakeWordManager.cleanup()
        }
    }
    
    return wakeWordManager
}
