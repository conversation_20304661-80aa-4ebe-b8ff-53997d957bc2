package io.livekit.android.example.voiceassistant.ui.banking.screens

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Backspace
import androidx.compose.material.icons.filled.Fingerprint
import androidx.compose.material.icons.filled.Security
import androidx.compose.material3.Button
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedButton
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import io.livekit.android.example.voiceassistant.managers.RpcManager
import io.livekit.android.example.voiceassistant.utils.BankAssistLogger
import io.livekit.android.room.participant.Participant
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.rememberCoroutineScope
import kotlinx.coroutines.launch

/**
 * PIN entry screen for authentication
 */
@Composable
fun PinEntryScreen(
    onPinEntered: (String) -> Unit,
    onBiometricRequested: () -> Unit,
    modifier: Modifier = Modifier,
    rpcManager: RpcManager? = null
) {
    var pin by remember { mutableStateOf("") }
    val maxPinLength = 4
    val coroutineScope = rememberCoroutineScope()

    // Handle PIN authentication with RPC completion
    fun handlePinAuthentication(enteredPin: String) {
        if (rpcManager != null) {
            coroutineScope.launch {
                try {
                    BankAssistLogger.i("PIN entered, completing authentication")

                    // Complete authentication through RpcManager (this handles the RPC response)
                    val authResponse = rpcManager.completeAuthentication(
                        success = true, // PIN was entered successfully
                        authMethod = "pin"
                    )

                    if (authResponse.success) {
                        BankAssistLogger.i("PIN authentication completed successfully: ${authResponse.message}")
                        onPinEntered(enteredPin) // Success callback
                    } else {
                        BankAssistLogger.w("PIN authentication completion failed: ${authResponse.message}")
                        onPinEntered("") // Indicate failure with empty string
                    }
                } catch (e: Exception) {
                    BankAssistLogger.e("PIN authentication error", e)
                    onPinEntered("") // Indicate failure
                }
            }
        } else {
            // Fallback when no RPC manager (for testing)
            BankAssistLogger.i("PIN authentication (no RPC)")
            onPinEntered(enteredPin)
        }
    }

    Column(
        modifier = modifier
            .fillMaxSize()
            .padding(24.dp),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center
    ) {
        // Header
        Card(
            modifier = Modifier.fillMaxWidth(),
            colors = CardDefaults.cardColors(
                containerColor = MaterialTheme.colorScheme.primaryContainer
            )
        ) {
            Column(
                modifier = Modifier.padding(24.dp),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Icon(
                    imageVector = Icons.Default.Security,
                    contentDescription = null,
                    modifier = Modifier.size(48.dp),
                    tint = MaterialTheme.colorScheme.onPrimaryContainer
                )
                Spacer(modifier = Modifier.height(16.dp))
                Text(
                    text = "Enter Your PIN",
                    style = MaterialTheme.typography.headlineSmall,
                    fontWeight = FontWeight.Bold,
                    color = MaterialTheme.colorScheme.onPrimaryContainer
                )
                Text(
                    text = "Please enter your 4-digit PIN to continue",
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onPrimaryContainer,
                    textAlign = TextAlign.Center
                )
            }
        }

        Spacer(modifier = Modifier.height(32.dp))

        // PIN Display
        Row(
            horizontalArrangement = Arrangement.spacedBy(16.dp),
            modifier = Modifier.padding(horizontal = 32.dp)
        ) {
            repeat(maxPinLength) { index ->
                PinDot(
                    filled = index < pin.length,
                    modifier = Modifier.size(20.dp)
                )
            }
        }

        Spacer(modifier = Modifier.height(32.dp))

        // Number Pad
        Column(
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            // Row 1: 1, 2, 3
            Row(
                horizontalArrangement = Arrangement.spacedBy(16.dp)
            ) {
                NumberButton("1") { if (pin.length < maxPinLength) pin += "1" }
                NumberButton("2") { if (pin.length < maxPinLength) pin += "2" }
                NumberButton("3") { if (pin.length < maxPinLength) pin += "3" }
            }

            // Row 2: 4, 5, 6
            Row(
                horizontalArrangement = Arrangement.spacedBy(16.dp)
            ) {
                NumberButton("4") { if (pin.length < maxPinLength) pin += "4" }
                NumberButton("5") { if (pin.length < maxPinLength) pin += "5" }
                NumberButton("6") { if (pin.length < maxPinLength) pin += "6" }
            }

            // Row 3: 7, 8, 9
            Row(
                horizontalArrangement = Arrangement.spacedBy(16.dp)
            ) {
                NumberButton("7") { if (pin.length < maxPinLength) pin += "7" }
                NumberButton("8") { if (pin.length < maxPinLength) pin += "8" }
                NumberButton("9") { if (pin.length < maxPinLength) pin += "9" }
            }

            // Row 4: Biometric, 0, Backspace
            Row(
                horizontalArrangement = Arrangement.spacedBy(16.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                // Biometric button
                IconButton(
                    onClick = onBiometricRequested,
                    modifier = Modifier
                        .size(72.dp)
                        .background(
                            MaterialTheme.colorScheme.secondaryContainer,
                            CircleShape
                        )
                ) {
                    Icon(
                        imageVector = Icons.Default.Fingerprint,
                        contentDescription = "Use Biometric",
                        tint = MaterialTheme.colorScheme.onSecondaryContainer
                    )
                }

                NumberButton("0") { if (pin.length < maxPinLength) pin += "0" }

                // Backspace button
                IconButton(
                    onClick = { if (pin.isNotEmpty()) pin = pin.dropLast(1) },
                    modifier = Modifier
                        .size(72.dp)
                        .background(
                            MaterialTheme.colorScheme.errorContainer,
                            CircleShape
                        )
                ) {
                    Icon(
                        imageVector = Icons.Default.Backspace,
                        contentDescription = "Delete",
                        tint = MaterialTheme.colorScheme.onErrorContainer
                    )
                }
            }
        }

        Spacer(modifier = Modifier.height(32.dp))

        // Submit Button
        Button(
            onClick = { handlePinAuthentication(pin) },
            enabled = pin.length == maxPinLength,
            modifier = Modifier.fillMaxWidth()
        ) {
            Text("Confirm PIN")
        }

        Spacer(modifier = Modifier.height(16.dp))

        // Alternative Authentication
        OutlinedButton(
            onClick = onBiometricRequested,
            modifier = Modifier.fillMaxWidth()
        ) {
            Icon(
                imageVector = Icons.Default.Fingerprint,
                contentDescription = null,
                modifier = Modifier.size(18.dp)
            )
            Spacer(modifier = Modifier.width(8.dp))
            Text("Use Biometric Authentication")
        }

        Spacer(modifier = Modifier.height(24.dp))

        // Security Notice
        Card(
            modifier = Modifier.fillMaxWidth(),
            colors = CardDefaults.cardColors(
                containerColor = MaterialTheme.colorScheme.surfaceVariant
            )
        ) {
            Column(modifier = Modifier.padding(16.dp)) {
                Text(
                    text = "🔒 Security Notice",
                    style = MaterialTheme.typography.labelMedium,
                    fontWeight = FontWeight.SemiBold
                )
                Text(
                    text = "Your PIN is encrypted and never stored on this device. For your security, never share your PIN with anyone.",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        }
    }
}

/**
 * Individual number button for the PIN pad
 */
@Composable
private fun NumberButton(
    number: String,
    onClick: () -> Unit
) {
    Box(
        modifier = Modifier
            .size(72.dp)
            .clip(CircleShape)
            .background(MaterialTheme.colorScheme.primaryContainer)
            .clickable { onClick() },
        contentAlignment = Alignment.Center
    ) {
        Text(
            text = number,
            style = MaterialTheme.typography.headlineMedium,
            fontWeight = FontWeight.Medium,
            color = MaterialTheme.colorScheme.onPrimaryContainer
        )
    }
}

/**
 * PIN dot indicator
 */
@Composable
private fun PinDot(
    filled: Boolean,
    modifier: Modifier = Modifier
) {
    Box(
        modifier = modifier
            .clip(CircleShape)
            .background(
                if (filled) {
                    MaterialTheme.colorScheme.primary
                } else {
                    Color.Transparent
                }
            )
            .border(
                width = 2.dp,
                color = MaterialTheme.colorScheme.primary,
                shape = CircleShape
            )
    )
}
