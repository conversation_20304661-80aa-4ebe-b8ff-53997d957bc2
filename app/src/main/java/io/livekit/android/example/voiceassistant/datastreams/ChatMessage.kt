package io.livekit.android.example.voiceassistant.datastreams

import io.livekit.android.room.participant.Participant
import io.livekit.android.room.types.TranscriptionSegment
import java.util.UUID

/**
 * Represents different types of messages in the chat
 */
sealed class ChatMessage {
    abstract val id: String
    abstract val identity: Participant.Identity
    abstract val timestamp: Long
    abstract val text: String

    /**
     * Message from voice transcription
     */
    data class TranscriptionMessage(
        override val identity: Participant.Identity,
        val transcriptionSegment: TranscriptionSegment,
    ) : ChatMessage() {
        override val id: String = transcriptionSegment.id
        override val timestamp: Long = transcriptionSegment.firstReceivedTime
        override val text: String = transcriptionSegment.text
    }

    /**
     * Manual text message sent via keyboard
     */
    data class TextMessage(
        override val id: String = UUID.randomUUID().toString(),
        override val identity: Participant.Identity,
        override val timestamp: Long = System.currentTimeMillis(),
        override val text: String,
        val isFromUser: Boolean = true
    ) : ChatMessage()
}

/**
 * Extension function to convert Transcription to ChatMessage
 */
fun Transcription.toChatMessage(): ChatMessage.TranscriptionMessage {
    return ChatMessage.TranscriptionMessage(
        identity = this.identity,
        transcriptionSegment = this.transcriptionSegment
    )
}
