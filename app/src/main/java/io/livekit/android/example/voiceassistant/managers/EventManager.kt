package io.livekit.android.example.voiceassistant.managers

import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import io.livekit.android.example.voiceassistant.utils.BankAssistLogger
import io.livekit.android.example.voiceassistant.data.BankingFunctions
import io.livekit.android.example.voiceassistant.data.ParsedFunctionResult
import io.livekit.android.example.voiceassistant.parsers.ServerFunctionResultParser
import io.livekit.android.room.Room
import io.livekit.android.room.participant.Participant
import io.livekit.android.events.collect
import kotlinx.coroutines.launch

/**
 * Event types for UI display
 */
sealed class LiveKitEvent {
    data class ParticipantConnected(
        val participant: Participant,
        val timestamp: Long = System.currentTimeMillis()
    ) : LiveKitEvent()
    
    data class ParticipantDisconnected(
        val participant: Participant,
        val timestamp: Long = System.currentTimeMillis()
    ) : LiveKitEvent()
    
    data class ConnectionStateChanged(
        val state: String,
        val timestamp: Long = System.currentTimeMillis()
    ) : LiveKitEvent()
    
    data class TrackPublished(
        val participant: Participant,
        val trackType: String,
        val timestamp: Long = System.currentTimeMillis()
    ) : LiveKitEvent()
    
    data class TrackUnpublished(
        val participant: Participant,
        val trackType: String,
        val timestamp: Long = System.currentTimeMillis()
    ) : LiveKitEvent()
    
    data class DataReceived(
        val participant: Participant,
        val dataSize: Int,
        val dataContent: String = "", // Decoded content for logging
        val timestamp: Long = System.currentTimeMillis()
    ) : LiveKitEvent()
    
    data class RoomMetadataChanged(
        val metadata: String,
        val timestamp: Long = System.currentTimeMillis()
    ) : LiveKitEvent()
    
    data class ParticipantMetadataChanged(
        val participant: Participant,
        val metadata: String,
        val timestamp: Long = System.currentTimeMillis()
    ) : LiveKitEvent()
    
    data class ConnectionQualityChanged(
        val participant: Participant,
        val quality: String,
        val timestamp: Long = System.currentTimeMillis()
    ) : LiveKitEvent()
    
    data class ActiveSpeakersChanged(
        val speakers: List<Participant>,
        val timestamp: Long = System.currentTimeMillis()
    ) : LiveKitEvent()
    
    data class ErrorOccurred(
        val error: String,
        val timestamp: Long = System.currentTimeMillis()
    ) : LiveKitEvent()
}

/**
 * Manages LiveKit room and participant events
 */
class EventManager(
    private val room: Room
) {
    private val _events = mutableStateListOf<LiveKitEvent>()
    val events: List<LiveKitEvent> = _events
    
    private val _connectionState = mutableStateListOf<String>()
    val connectionState: List<String> = _connectionState
    
    private val _participants = mutableStateListOf<Participant>()
    val participants: List<Participant> = _participants

    /**
     * Start listening to room events
     */
    fun startEventListening() {
        try {
            BankAssistLogger.i("Starting LiveKit event listening")

            // Initialize participants list
            _participants.clear()
            _participants.addAll(room.remoteParticipants.values)
            if (room.localParticipant != null) {
                _participants.add(room.localParticipant)
            }

            // Register data received handlers for all participants
            registerDataReceivedHandlers()

            addEvent(LiveKitEvent.ConnectionStateChanged("Event listening started"))

        } catch (e: Exception) {
            BankAssistLogger.e("Failed to start event listening", e)
            addEvent(LiveKitEvent.ErrorOccurred("Failed to start event listening: ${e.message}"))
        }
    }

    /**
     * Register data received handlers for server events
     */
    private fun registerDataReceivedHandlers() {
        try {
            // The actual event handling is done in handleRoomEvents() using room.events.collect
            // This method is kept for logging purposes
            BankAssistLogger.i("Data received handlers will be registered via room.events.collect")
            BankAssistLogger.d("Current participants: ${_participants.size}")
            _participants.forEach { participant ->
                BankAssistLogger.d("Participant: ${participant.identity?.value ?: "unknown"}")
            }
        } catch (e: Exception) {
            BankAssistLogger.e("Failed to register data received handlers", e)
        }
    }

    /**
     * Handle incoming data from server participants (called by LiveKit event system)
     */
    fun handleDataReceived(participant: io.livekit.android.room.participant.RemoteParticipant?, data: ByteArray) {
        try {
            val participantIdentity = participant?.identity?.value ?: "server"

            // Log the server event using BankAssistLogger
            BankAssistLogger.logServerEvent(data, participantIdentity)

            // Decode data for event logging
            val decodedContent = try {
                String(data, Charsets.UTF_8)
            } catch (e: Exception) {
                "Binary data (${data.size} bytes)"
            }

            // Convert RemoteParticipant to Participant for event logging
            val eventParticipant = participant as? Participant ?: run {
                // If participant is null (server data), create a placeholder
                BankAssistLogger.d("Data received from server (no specific participant)")
                null
            }

            // Add to events list only if we have a participant
            if (eventParticipant != null) {
                addEvent(LiveKitEvent.DataReceived(
                    participant = eventParticipant,
                    dataSize = data.size,
                    dataContent = decodedContent.take(100), // Truncate for display
                    timestamp = System.currentTimeMillis()
                ))
            } else {
                // Log server data without adding to events (since we need a participant for the event)
                BankAssistLogger.i("Server data received: ${decodedContent.take(100)}...")
            }

            // Process specific server events based on content
            processServerEvent(eventParticipant, decodedContent, data)

        } catch (e: Exception) {
            BankAssistLogger.e("Error handling data received from ${participant?.identity?.value ?: "server"}", e)
            addEvent(LiveKitEvent.ErrorOccurred("Data processing error: ${e.message}"))
        }
    }

    /**
     * Process specific server events based on content
     */
    private fun processServerEvent(participant: Participant?, content: String, rawData: ByteArray) {
        try {
            when {
                // Check for JSON structured data
                content.trim().startsWith("{") && content.trim().endsWith("}") -> {
                    BankAssistLogger.i("Received JSON data from server: ${content.take(200)}...")
                    // Could parse JSON and trigger specific actions
                }

                // Check for specific command patterns
                content.contains("balance", ignoreCase = true) -> {
                    BankAssistLogger.i("Server sent balance-related data")
                }

                content.contains("transfer", ignoreCase = true) -> {
                    BankAssistLogger.i("Server sent transfer-related data")
                }

                content.contains("error", ignoreCase = true) -> {
                    BankAssistLogger.w("Server sent error message: $content")
                    addEvent(LiveKitEvent.ErrorOccurred("Server error: $content"))
                }

                // Handle other text content
                content.isNotBlank() -> {
                    BankAssistLogger.i("Server sent text data: ${content.take(100)}...")
                }

                // Handle binary data
                else -> {
                    BankAssistLogger.i("Server sent binary data: ${rawData.size} bytes")
                }
            }
        } catch (e: Exception) {
            BankAssistLogger.e("Error processing server event", e)
        }
    }

    /**
     * Handle room events using coroutines - Real implementation with LiveKit event system
     */
    suspend fun handleRoomEvents() {
        try {
            BankAssistLogger.i("Starting real-time room event monitoring")
            addEvent(LiveKitEvent.ConnectionStateChanged("Event monitoring active"))

            // Real LiveKit event handling using room.events.collect
            room.events.collect { event ->
                when (event) {
                    is io.livekit.android.events.RoomEvent.DataReceived -> {
                        BankAssistLogger.d("Received DataReceived event from LiveKit")
                        handleDataReceived(event.participant, event.data)

                        // Log additional event details
                        BankAssistLogger.d("Event details - Topic: ${event.topic}, Data size: ${event.data.size}")
                    }

                    is io.livekit.android.events.RoomEvent.ParticipantConnected -> {
                        BankAssistLogger.i("Participant connected: ${event.participant.identity}")
                        _participants.add(event.participant)
                        addEvent(LiveKitEvent.ParticipantConnected(event.participant))
                    }

                    is io.livekit.android.events.RoomEvent.ParticipantDisconnected -> {
                        BankAssistLogger.i("Participant disconnected: ${event.participant.identity}")
                        _participants.remove(event.participant)
                        addEvent(LiveKitEvent.ParticipantDisconnected(event.participant))
                    }

                    is io.livekit.android.events.RoomEvent.TrackPublished -> {
                        BankAssistLogger.d("Track published by ${event.participant.identity}: ${event.publication.kind}")
                        addEvent(LiveKitEvent.TrackPublished(
                            participant = event.participant,
                            trackType = event.publication.kind.toString()
                        ))
                    }

                    is io.livekit.android.events.RoomEvent.TrackUnpublished -> {
                        BankAssistLogger.d("Track unpublished by ${event.participant.identity}: ${event.publication.kind}")
                        addEvent(LiveKitEvent.TrackUnpublished(
                            participant = event.participant,
                            trackType = event.publication.kind.toString()
                        ))
                    }

                    is io.livekit.android.events.RoomEvent.ActiveSpeakersChanged -> {
                        BankAssistLogger.d("Active speakers changed: ${event.speakers.size} speakers")
                        addEvent(LiveKitEvent.ActiveSpeakersChanged(event.speakers))
                    }

                    is io.livekit.android.events.RoomEvent.RoomMetadataChanged -> {
                        BankAssistLogger.d("Room metadata changed: ${event.newMetadata}")
                        addEvent(LiveKitEvent.RoomMetadataChanged(event.newMetadata ?: ""))
                    }

                    is io.livekit.android.events.RoomEvent.ParticipantMetadataChanged -> {
                        BankAssistLogger.d("Participant metadata changed: ${event.participant.identity}")
                        addEvent(LiveKitEvent.ParticipantMetadataChanged(
                            participant = event.participant,
                            metadata = event.participant.metadata ?: ""
                        ))
                    }

                    else -> {
                        // Log other events for debugging
                        BankAssistLogger.d("Other room event: ${event::class.simpleName}")
                    }
                }
            }

        } catch (e: Exception) {
            BankAssistLogger.e("Error handling room events: ${e.message}", e)
            addEvent(LiveKitEvent.ErrorOccurred("Event handling error: ${e.message}"))
        }
    }

    /**
     * Get current room statistics
     */
    fun getRoomStats(): Map<String, Any> {
        return try {
            mapOf(
                "participantCount" to _participants.size,
                "localParticipant" to (room.localParticipant?.identity?.value ?: "unknown"),
                "connectionState" to "Connected", // Simplified
                "eventCount" to _events.size,
                "roomName" to (room.name ?: "unknown"),
                "roomSid" to (room.sid ?: "unknown")
            )
        } catch (e: Exception) {
            BankAssistLogger.e("Error getting room stats", e)
            mapOf("error" to e.message.orEmpty())
        }
    }

    /**
     * Get participant by identity
     */
    fun getParticipant(identity: Participant.Identity): Participant? {
        return _participants.find { it.identity == identity }
    }

    /**
     * Get all remote participants
     */
    fun getRemoteParticipants(): List<Participant> {
        return _participants.filter { it != room.localParticipant }
    }

    /**
     * Clear old events to prevent memory issues
     */
    fun clearOldEvents(maxEvents: Int = 100) {
        if (_events.size > maxEvents) {
            val eventsToRemove = _events.size - maxEvents
            repeat(eventsToRemove) {
                _events.removeAt(0)
            }
            BankAssistLogger.d("Cleared $eventsToRemove old events")
        }
    }

    private fun addEvent(event: LiveKitEvent) {
        _events.add(event)
        
        // Auto-clear old events
        clearOldEvents()

        BankAssistLogger.d("Added event: ${event::class.simpleName}")
    }

    fun cleanup() {
        try {
            _events.clear()
            _connectionState.clear()
            _participants.clear()

            BankAssistLogger.d("Cleaned up event manager")
        } catch (e: Exception) {
            BankAssistLogger.e("Error during event manager cleanup", e)
        }
    }
}

/**
 * Composable function to create and manage EventManager
 */
@Composable
fun rememberEventManager(room: Room): EventManager {
    val coroutineScope = rememberCoroutineScope()
    
    val eventManager = remember(room) {
        EventManager(room).apply {
            startEventListening()
            coroutineScope.launch {
                handleRoomEvents()
            }
        }
    }

    DisposableEffect(room) {
        onDispose {
            eventManager.cleanup()
        }
    }

    return eventManager
}
